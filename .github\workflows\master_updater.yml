name: Master branch updater.

on:
  release:
    types: released

jobs:
  updater:
    runs-on: ubuntu-latest
    env:
      SKIP: "0"
      to_branch: "master"
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
        if: env.SKIP == '0'
      - name: Push the code to the master branch.
        if: env.SKIP == '0'
        run: |
          token=${{ secrets.TOKEN_FOR_MASTER_UPDATER }}
          if [ -z "${token}" ]; then
              echo "Token is unset. Nothing to do."
              exit 0
          fi

          url=https://x-access-token:$<EMAIL>/$GITHUB_REPOSITORY
          latest_tag=$(git describe --tags --abbrev=0)
          echo "Latest tag: $latest_tag"

          git remote set-url origin $url
          git remote -v
          git checkout master
          git merge $latest_tag

          git push origin HEAD:refs/heads/$to_branch
          echo "Done!"
