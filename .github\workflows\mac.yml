name: MacOS.

on:
  push:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'changelog.txt'
      - 'LEGAL'
      - 'LICENSE'
      - '.github/**'
      - '!.github/workflows/mac.yml'
      - 'lib/xdg/**'
      - 'snap/**'
      - 'Telegram/build/docker/**'
      - 'Telegram/Resources/uwp/**'
      - 'Telegram/Resources/winrc/**'
      - 'Telegram/SourceFiles/platform/win/**'
      - 'Telegram/SourceFiles/platform/linux/**'
      - 'Telegram/configure.bat'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'changelog.txt'
      - 'LEGAL'
      - 'LICENSE'
      - '.github/**'
      - '!.github/workflows/mac.yml'
      - 'lib/xdg/**'
      - 'snap/**'
      - 'Telegram/build/docker/**'
      - 'Telegram/Resources/uwp/**'
      - 'Telegram/Resources/winrc/**'
      - 'Telegram/SourceFiles/platform/win/**'
      - 'Telegram/SourceFiles/platform/linux/**'
      - 'Telegram/configure.bat'

jobs:

  macos:
    name: MacOS
    runs-on: macos-13

    strategy:
      matrix:
        defines:
          - ""
    env:
      UPLOAD_ARTIFACT: "true"
      ONLY_CACHE: "false"
      PREPARE_PATH: "Telegram/build/prepare/prepare.py"

    steps:
      - name: Get repository name.
        run: echo "REPO_NAME=${GITHUB_REPOSITORY##*/}" >> $GITHUB_ENV

      - name: Clone.
        uses: actions/checkout@v4
        with:
          submodules: recursive
          path: ${{ env.REPO_NAME }}

      - name: First set up.
        run: |
          sudo chown -R `whoami`:admin /usr/local/share

          brew update
          brew upgrade || true
          brew install automake meson nasm ninja pkg-config

          # Disable spotlight.
          sudo mdutil -a -i off

          sudo xcode-select -s /Applications/Xcode.app/Contents/Developer

      - name: ThirdParty cache.
        id: cache-third-party
        uses: actions/cache@v4
        with:
          path: ThirdParty
          key: ${{ runner.OS }}-third-party-${{ hashFiles(format('{0}/{1}', env.REPO_NAME, env.PREPARE_PATH)) }}
          restore-keys: ${{ runner.OS }}-third-party-

      - name: Libraries cache.
        id: cache-libs
        uses: actions/cache@v4
        with:
          path: Libraries
          key: ${{ runner.OS }}-libs-${{ hashFiles(format('{0}/{1}', env.REPO_NAME, env.PREPARE_PATH)) }}
          restore-keys: ${{ runner.OS }}-libs-

      - name: Libraries.
        run: |
          ./$REPO_NAME/Telegram/build/prepare/mac.sh skip-release silent

      - name: Free up some disk space.
        run: |
          cd Libraries
          find . -iname "*.dir" -exec rm -rf {} || true \;

      - name: Telegram Desktop build.
        if: env.ONLY_CACHE == 'false'
        run: |
          cd $REPO_NAME/Telegram

          DEFINE=""
          if [ -n "${{ matrix.defines }}" ]; then
            DEFINE="-D ${{ matrix.defines }}=ON"
            echo Define from matrix: $DEFINE
            echo "ARTIFACT_NAME=Telegram_${{ matrix.defines }}" >> $GITHUB_ENV
          else
            echo "ARTIFACT_NAME=Telegram" >> $GITHUB_ENV
          fi

          ./configure.sh \
          -D CMAKE_CONFIGURATION_TYPES=Debug \
          -D CMAKE_C_FLAGS="-Werror" \
          -D CMAKE_CXX_FLAGS="-Werror" \
          -D CMAKE_XCODE_ATTRIBUTE_CODE_SIGNING_ALLOWED=NO \
          -D TDESKTOP_API_TEST=ON \
          -D DESKTOP_APP_DISABLE_AUTOUPDATE=OFF \
          -D DESKTOP_APP_DISABLE_CRASH_REPORTS=OFF \
          $DEFINE

          cmake --build ../out --config Debug --parallel

      - name: Move artifact.
        if: env.UPLOAD_ARTIFACT == 'true'
        run: |
          cd $REPO_NAME/out/Debug
          mkdir artifact
          mv Telegram.app artifact/
          mv Updater artifact/
      - uses: actions/upload-artifact@v4
        if: env.UPLOAD_ARTIFACT == 'true'
        name: Upload artifact.
        with:
          name: ${{ env.ARTIFACT_NAME }}
          path: ${{ env.REPO_NAME }}/out/Debug/artifact/
