# Adapted from write_basic_package_version_file(... COMPATIBILITY SameMajorVersion) output
# ARCH_INDEPENDENT is only present in cmake 3.14 and onwards

set( PACKAGE_VERSION "@package_version@" )

if( PACKAGE_VERSION VERSION_LESS PACKAGE_FIND_VERSION )
    set( PACKAGE_VERSION_COMPATIBLE FALSE )
else()
    if( "@package_version@" MATCHES "^([0-9]+)\\." )
        set( CVF_VERSION_MAJOR "${CMAKE_MATCH_1}" )
    else()
        set( CVF_VERSION_MAJOR "@package_version@" )
    endif()

    if( PACKAGE_FIND_VERSION_MAJOR STREQUAL CVF_VERSION_MAJOR )
        set( PACKAGE_VERSION_COMPATIBLE TRUE )
    else()
        set( PACKAGE_VERSION_COMPATIBLE FALSE )
    endif()

    if( PACKAGE_FIND_VERSION STREQUAL PACKAGE_VERSION )
        set( PACKAGE_VERSION_EXACT TRUE )
    endif()
endif()
