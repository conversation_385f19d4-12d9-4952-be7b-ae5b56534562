# Тестирование интеграции WebSocket поиска в Telegram

## Что было реализовано

### 1. WebSocket клиент в Telegram
- **Файлы**: `Telegram/SourceFiles/core/websocket_search_client.h`, `websocket_search_client.cpp`
- **Функциональность**: Подключение к серверу `wss://discord-6r4h.onrender.com`, регистрация как Discord плагин, отправка поисковых запросов

### 2. Система кодов активации
- **Файлы**: `Telegram/SourceFiles/boxes/websocket_activation_box.h`, `websocket_activation_box.cpp`
- **Функциональность**: Диалоговое окно для ввода/генерации кода активации, отображение статуса подключения

### 3. Интеграция в настройки
- **Файл**: `Telegram/SourceFiles/settings/settings_advanced.cpp`
- **Функциональность**: Добавлен пункт "Поиск в таблице" в раздел "Дополнительно"

### 4. Контекстное меню
- **Файл**: `Telegram/SourceFiles/history/history_inner_widget.cpp`
- **Функциональность**: Добавлен пункт "Поиск в таблице" при выделении текста

### 5. Обновление прокси-сервера
- **Файл**: `myPluginAndServer/proxy-server.js`
- **Функциональность**: Добавлена обработка сообщений `FIND_ORDER_REQUEST` от Telegram клиента

## Архитектура

```
Telegram Client ──WebSocket──> Proxy Server ──WebSocket──> Chrome Extension
                │                    │
                └──HTTP POST──────────┘
                                     │
                              Discord Plugin
```

### Протокол сообщений

1. **Регистрация Telegram клиента (WebSocket)**:
   ```json
   {
     "type": "DISCORD_PLUGIN_REGISTER",
     "pluginId": "TelegramClient_123456789",
     "activationCode": "123456"
   }
   ```

2. **Поиск текста от Telegram (HTTP POST к /api/find-order)**:
   ```json
   {
     "orderNumber": "выделенный текст",
     "rowNumber": 1,
     "pluginId": "TelegramClient_123456789"
   }
   ```

3. **Команда поиска в Chrome расширение (WebSocket)**:
   ```json
   {
     "type": "FIND_ORDER_COMMAND",
     "orderNumber": "выделенный текст",
     "rowNumber": 1,
     "timestamp": 1234567890
   }
   ```

## Как тестировать

### 1. Компиляция Telegram
```bash
# В корне проекта Telegram
mkdir build
cd build
cmake ..
make
```

### 2. Запуск прокси-сервера
```bash
cd myPluginAndServer
npm install ws
node proxy-server.js
```

### 3. Тестирование функциональности

1. **Активация**:
   - Запустить Telegram
   - Перейти в Настройки > Дополнительно > Поиск в таблице
   - Скопировать код активации
   - Ввести код в Chrome расширение

2. **Поиск**:
   - Выделить текст в любом чате Telegram
   - ПКМ > "Поиск в таблице"
   - Проверить, что команда дошла до Chrome расширения

### 4. Отладка

- Логи WebSocket соединений в консоли браузера (F12)
- Логи прокси-сервера в терминале
- Уведомления в Telegram при ошибках подключения

## Возможные проблемы

1. **Компиляция**: Могут потребоваться дополнительные зависимости Qt WebSockets
2. **Подключение**: Проверить доступность сервера `discord-6r4h.onrender.com`
3. **Активация**: Убедиться, что коды активации совпадают между Telegram и Chrome расширением

## Обновленная архитектура (без изменений сервера)

### Принцип работы:
- **WebSocket** используется только для регистрации и активации Telegram клиента
- **HTTP POST** используется для отправки поисковых запросов (точно как Discord плагин)
- **Прокси-сервер** обрабатывает Telegram как обычный Discord плагин
- **Никаких изменений** в прокси-сервере не требуется

### Схема:
```
Telegram ──WebSocket──> Proxy Server ──WebSocket──> Chrome Extension
         │                    │
         └──HTTP POST──────────┘
                             │
                      Discord Plugin
```

## Следующие шаги

1. Протестировать компиляцию и исправить ошибки
2. Убедиться, что Chrome расширение правильно обрабатывает команды `FIND_ORDER_COMMAND`
3. Реализовать поиск по тексту в таблице (не только по номеру заказа)
4. Добавить индикаторы статуса подключения в интерфейс Telegram
