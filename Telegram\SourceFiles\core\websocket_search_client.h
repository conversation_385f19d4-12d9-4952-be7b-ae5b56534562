/*
This file is part of Telegram Desktop,
the official desktop application for the Telegram messaging service.

For license and copyright information please follow this link:
https://github.com/telegramdesktop/tdesktop/blob/master/LEGAL
*/
#pragma once

#include "base/timer.h"
#include "base/weak_ptr.h"
#include "base/object_ptr.h"

#include <QtCore/QObject>
#include <QtCore/QJsonObject>
#include <QtCore/QJsonDocument>
#include <QtNetwork/QNetworkAccessManager>
#include <QtNetwork/QTcpSocket>

#include <rpl/producer.h>
#include <rpl/lifetime.h>
#include <rpl/event_stream.h>

namespace Core {

class WebSocketSearchClient final : public base::has_weak_ptr {

public:
	WebSocketSearchClient();
	~WebSocketSearchClient();

	void start();
	void stop();

	// Отправка поиска в таблице (аналогично Discord плагину)
	void sendSearchInTable(const QString &searchText);
	void sendFindOrderRequest(const QString &orderNumber, int rowNumber);

	// WebSocket подключение к серверу
	void connectToWebSocketServer();
	void registerAsDiscordPlugin();
	void handleWebSocketMessage(const QJsonObject &message);

	// Получение и установка кода активации
	[[nodiscard]] QString activationCode() const;
	void setActivationCode(const QString &code);

	// Проверка статуса подключения
	[[nodiscard]] bool isConnected() const;
	[[nodiscard]] bool isActivated() const;
	void setActivated(bool activated);

	// Сигналы (заменяем на обычные функции для упрощения)
	rpl::producer<> connectedSignal() const;
	rpl::producer<> disconnectedSignal() const;
	rpl::producer<> activationSuccessSignal() const;
	rpl::producer<QString> activationErrorSignal() const;
	rpl::producer<std::pair<bool, QString>> searchResultSignal() const;

private:
	void onConnected();
	void onDisconnected();
	void onDataReceived();
	void onError(QAbstractSocket::SocketError error);

private:
	void connectToServer();
	void scheduleReconnect();
	void sendMessage(const QJsonObject &message);
	void handleMessage(const QJsonObject &data);
	void registerTelegramClient();
	void loadSettings();
	void saveSettings();

	QTcpSocket *_tcpSocket = nullptr;
	QNetworkAccessManager *_networkManager = nullptr;
	QString _serverHost = "discord-6r4h.onrender.com";
	QString _httpServerUrl = "https://discord-6r4h.onrender.com";
	QString _activationCode;
	QString _clientId;
	bool _isActivated = false;
	
	base::Timer _reconnectTimer;
	int _reconnectAttempts = 0;
	static constexpr int kMaxReconnectAttempts = 10;
	static constexpr int kReconnectBaseDelay = 1000; // 1 second
	static constexpr int kMaxReconnectDelay = 30000; // 30 seconds

	// Event streams для сигналов
	rpl::event_stream<> _connectedStream;
	rpl::event_stream<> _disconnectedStream;
	rpl::event_stream<> _activationSuccessStream;
	rpl::event_stream<QString> _activationErrorStream;
	rpl::event_stream<std::pair<bool, QString>> _searchResultStream;

	rpl::lifetime _lifetime;
};

} // namespace Core
