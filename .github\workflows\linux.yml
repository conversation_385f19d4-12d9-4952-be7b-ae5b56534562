name: Linux.

on:
  push:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'changelog.txt'
      - 'LEGAL'
      - 'LICENSE'
      - '.github/**'
      - '!.github/workflows/linux.yml'
      - 'snap/**'
      - 'Telegram/build/**'
      - '!Telegram/build/docker/centos_env/**'
      - 'Telegram/Resources/uwp/**'
      - 'Telegram/Resources/winrc/**'
      - 'Telegram/SourceFiles/platform/win/**'
      - 'Telegram/SourceFiles/platform/mac/**'
      - 'Telegram/Telegram/**'
      - 'Telegram/configure.bat'
      - 'Telegram/Telegram.plist'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'changelog.txt'
      - 'LEGAL'
      - 'LICENSE'
      - '.github/**'
      - '!.github/workflows/linux.yml'
      - 'snap/**'
      - 'Telegram/build/**'
      - '!Telegram/build/docker/centos_env/**'
      - 'Telegram/Resources/uwp/**'
      - 'Telegram/Resources/winrc/**'
      - 'Telegram/SourceFiles/platform/win/**'
      - 'Telegram/SourceFiles/platform/mac/**'
      - 'Telegram/Telegram/**'
      - 'Telegram/configure.bat'
      - 'Telegram/Telegram.plist'

jobs:

  linux:
    name: Rocky Linux 8
    runs-on: ubuntu-latest

    strategy:
      matrix:
        defines:
          - ""
          - "DESKTOP_APP_DISABLE_X11_INTEGRATION"

    env:
      UPLOAD_ARTIFACT: "true"
      ONLY_CACHE: "false"
      IMAGE_TAG: tdesktop:centos_env

    steps:
      - name: Clone.
        uses: actions/checkout@v4
        with:
          submodules: recursive

      - name: First set up.
        run: |
          sudo apt update
          curl -sSL https://install.python-poetry.org | python3 -
          cd Telegram/build/docker/centos_env
          poetry install
          DOCKERFILE=$(DEBUG= LTO= poetry run gen_dockerfile)
          echo "$DOCKERFILE" > Dockerfile

      - name: Free up some disk space.
        uses: jlumbroso/free-disk-space@54081f138730dfa15788a46383842cd2f914a1be
        with:
          tool-cache: true

      - name: Set up Docker Buildx.
        uses: docker/setup-buildx-action@v3

      - name: Libraries.
        uses: docker/build-push-action@v6
        with:
          context: Telegram/build/docker/centos_env
          load: ${{ env.ONLY_CACHE == 'false' }}
          tags: ${{ env.IMAGE_TAG }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Telegram Desktop build.
        if: env.ONLY_CACHE == 'false'
        run: |
          DEFINE=""
          if [ -n "${{ matrix.defines }}" ]; then
            DEFINE="-D ${{ matrix.defines }}=ON"
            echo Define from matrix: $DEFINE
            echo "ARTIFACT_NAME=Telegram_${{ matrix.defines }}" >> $GITHUB_ENV
          else
            echo "ARTIFACT_NAME=Telegram" >> $GITHUB_ENV
          fi

          docker run --rm \
          -u $(id -u) \
          -v $PWD:/usr/src/tdesktop \
          -e CONFIG=Debug \
          $IMAGE_TAG \
          /usr/src/tdesktop/Telegram/build/docker/centos_env/build.sh \
          -D CMAKE_CONFIGURATION_TYPES=Debug \
          -D CMAKE_C_FLAGS_DEBUG="" \
          -D CMAKE_CXX_FLAGS_DEBUG="" \
          -D CMAKE_C_FLAGS="-Werror" \
          -D CMAKE_CXX_FLAGS="-Werror" \
          -D CMAKE_EXE_LINKER_FLAGS="-s" \
          -D TDESKTOP_API_TEST=ON \
          -D DESKTOP_APP_DISABLE_AUTOUPDATE=OFF \
          -D DESKTOP_APP_DISABLE_CRASH_REPORTS=OFF \
          $DEFINE

      - name: Check.
        if: env.ONLY_CACHE == 'false'
        run: |
          filePath="out/Debug/Telegram"
          if test -f "$filePath"; then
            echo "Build successfully done! :)"

            size=$(stat -c %s "$filePath")
            echo "File size of ${filePath}: ${size} Bytes."
          else
            echo "Build error, output file does not exist."
            exit 1
          fi

      - name: Move artifact.
        if: env.UPLOAD_ARTIFACT == 'true'
        run: |
          cd out/Debug
          mkdir artifact
          mv {Telegram,Updater} artifact/
      - uses: actions/upload-artifact@v4
        if: env.UPLOAD_ARTIFACT == 'true'
        name: Upload artifact.
        with:
          name: ${{ env.ARTIFACT_NAME }}
          path: out/Debug/artifact/
