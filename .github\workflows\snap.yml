name: Snap.

on:
  push:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'changelog.txt'
      - 'LEGAL'
      - 'LICENSE'
      - '.github/**'
      - '!.github/workflows/snap.yml'
      - 'Telegram/build/**'
      - 'Telegram/Resources/uwp/**'
      - 'Telegram/Resources/winrc/**'
      - 'Telegram/SourceFiles/platform/win/**'
      - 'Telegram/SourceFiles/platform/mac/**'
      - 'Telegram/Telegram/**'
      - 'Telegram/configure.bat'
      - 'Telegram/Telegram.plist'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'changelog.txt'
      - 'LEGAL'
      - 'LICENSE'
      - '.github/**'
      - '!.github/workflows/snap.yml'
      - 'Telegram/build/**'
      - 'Telegram/Resources/uwp/**'
      - 'Telegram/Resources/winrc/**'
      - 'Telegram/SourceFiles/platform/win/**'
      - 'Telegram/SourceFiles/platform/mac/**'
      - 'Telegram/Telegram/**'
      - 'Telegram/configure.bat'
      - 'Telegram/Telegram.plist'

jobs:

  snap:
    name: Ubuntu
    runs-on: ubuntu-latest

    env:
      UPLOAD_ARTIFACT: "true"

    steps:
      - name: Clone.
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: recursive

      - name: First set up.
        run: |
          sudo iptables -P FORWARD ACCEPT
          sudo snap install --classic snapcraft
          sudo usermod -aG lxd $USER
          sudo lxd init --auto
          sudo lxd waitready

      - name: Free up some disk space.
        uses: jlumbroso/free-disk-space@54081f138730dfa15788a46383842cd2f914a1be

      - name: Telegram Desktop snap build.
        run: sudo -u $USER snap run snapcraft --verbosity=debug

      - name: Move artifact.
        if: env.UPLOAD_ARTIFACT == 'true'
        run: |
          artifact_name=$(echo telegram-desktop_*.snap)
          echo "ARTIFACT_NAME=$artifact_name" >> $GITHUB_ENV

          mkdir artifact
          mv $artifact_name artifact

      - uses: actions/upload-artifact@v4
        if: env.UPLOAD_ARTIFACT == 'true'
        name: Upload artifact.
        with:
          name: ${{ env.ARTIFACT_NAME }}
          path: artifact
