name: Windows.

on:
  push:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'changelog.txt'
      - 'LEGAL'
      - 'LICENSE'
      - '.github/**'
      - '!.github/workflows/win.yml'
      - 'lib/xdg/**'
      - 'snap/**'
      - 'Telegram/build/docker/**'
      - 'Telegram/Resources/uwp/**'
      - 'Telegram/SourceFiles/platform/linux/**'
      - 'Telegram/SourceFiles/platform/mac/**'
      - 'Telegram/Telegram/**'
      - 'Telegram/configure.sh'
      - 'Telegram/Telegram.plist'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '**.md'
      - 'changelog.txt'
      - 'LEGAL'
      - 'LICENSE'
      - '.github/**'
      - '!.github/workflows/win.yml'
      - 'lib/xdg/**'
      - 'snap/**'
      - 'Telegram/build/docker/**'
      - 'Telegram/Resources/uwp/**'
      - 'Telegram/SourceFiles/platform/linux/**'
      - 'Telegram/SourceFiles/platform/mac/**'
      - 'Telegram/Telegram/**'
      - 'Telegram/configure.sh'
      - 'Telegram/Telegram.plist'

jobs:

  windows:
    name: Windows
    runs-on: windows-latest

    strategy:
      matrix:
        arch: [Win32, x64]
        generator: ["", "Ninja Multi-Config"]

    env:
      UPLOAD_ARTIFACT: "true"
      ONLY_CACHE: "false"
      PREPARE_PATH: "Telegram/build/prepare/prepare.py"

    defaults:
      run:
        shell: cmd

    steps:
      - name: Prepare directories.
        run: |
          mkdir %userprofile%\TBuild\Libraries
          mklink /d %GITHUB_WORKSPACE%\TBuild %userprofile%\TBuild
          echo TBUILD=%GITHUB_WORKSPACE%\TBuild>>%GITHUB_ENV%

      - name: Get repository name.
        shell: bash
        run: echo "REPO_NAME=${GITHUB_REPOSITORY##*/}" >> $GITHUB_ENV

      - uses: ilammy/msvc-dev-cmd@v1.13.0
        name: Native Tools Command Prompt.
        with:
          arch: ${{ matrix.arch }}

      - name: Clone.
        uses: actions/checkout@v4
        with:
          submodules: recursive
          path: ${{ env.TBUILD }}\${{ env.REPO_NAME }}

      - name: Set up environment paths.
        shell: bash
        run: |
          echo "CACHE_KEY=$(sha256sum $TBUILD/$REPO_NAME/$PREPARE_PATH | awk '{ print $1 }')" >> $GITHUB_ENV

          echo "Configurate git for cherry-picks."
          git config --global user.email "<EMAIL>"
          git config --global user.name "Sample"

      - name: NuGet sources.
        run: |
          nuget sources Disable -Name "Microsoft Visual Studio Offline Packages"
          nuget sources Add -Source https://api.nuget.org/v3/index.json & exit 0

      - name: ThirdParty cache.
        id: cache-third-party
        uses: actions/cache@v4
        with:
          path: ${{ env.TBUILD }}\ThirdParty
          key: ${{ runner.OS }}-${{ matrix.arch }}-third-party-${{ env.CACHE_KEY }}
          restore-keys: ${{ runner.OS }}-${{ matrix.arch }}-third-party-

      - name: Libraries cache.
        id: cache-libs
        uses: actions/cache@v4
        with:
          path: ${{ env.TBUILD }}\Libraries
          key: ${{ runner.OS }}-${{ matrix.arch }}-libs-${{ env.CACHE_KEY }}
          restore-keys: ${{ runner.OS }}-${{ matrix.arch }}-libs-

      - name: Libraries.
        env:
          GYP_MSVS_OVERRIDE_PATH: 'C:\Program Files\Microsoft Visual Studio\2022\Enterprise\'
          GYP_MSVS_VERSION: 2022
        run: |
          cd %TBUILD%
          %REPO_NAME%\Telegram\build\prepare\win.bat skip-release silent

      - name: Read configuration matrix.
        shell: bash
        run: |
          ARTIFACT_NAME="Telegram"

          ARCH=""
          if [ -n "${{ matrix.arch }}" ]; then
            case "${{ matrix.arch }}" in
              Win32) ARCH="x86";;
              *) ARCH="${{ matrix.arch }}";;
            esac
            echo "Architecture from matrix: $ARCH"
            ARTIFACT_NAME="${ARTIFACT_NAME}_${{ matrix.arch }}"
          fi

          GENERATOR=""
          if [ -n "${{ matrix.generator }}" ]; then
            GENERATOR="-G \"${{ matrix.generator }}\""
            echo "Generator from matrix: $GENERATOR"
            ARTIFACT_NAME="${ARTIFACT_NAME}_${{ matrix.generator }}"
          fi
          echo "TDESKTOP_BUILD_GENERATOR=$GENERATOR" >> $GITHUB_ENV

          [ -n "$GENERATOR" ] && ARCH=""
          echo "TDESKTOP_BUILD_ARCH=$ARCH" >> $GITHUB_ENV

          DEFINE=""
          if [ -n "${{ matrix.defines }}" ]; then
            DEFINE="-D ${{ matrix.defines }}=ON"
            echo "Define from matrix: $DEFINE"
            ARTIFACT_NAME="${ARTIFACT_NAME}_${{ matrix.defines }}"
          fi
          echo "TDESKTOP_BUILD_DEFINE=$DEFINE" >> $GITHUB_ENV

          echo "ARTIFACT_NAME=$ARTIFACT_NAME" >> $GITHUB_ENV

          API="-D TDESKTOP_API_TEST=ON"
          if [ $GITHUB_REF == 'refs/heads/nightly' ]; then
            echo "Use the open credentials."
            API="-D TDESKTOP_API_ID=611335 -D TDESKTOP_API_HASH=d524b414d21f4d37f08684c1df41ac9c"
          fi
          echo "TDESKTOP_BUILD_API=$API" >> $GITHUB_ENV

      - name: Free up some disk space.
        run: |
          cd %TBUILD%
          del /S Libraries\*.pdb
          del /S Libraries\*.pch
          del /S Libraries\*.obj

      - name: Telegram Desktop build.
        if: env.ONLY_CACHE == 'false'
        run: |
          cd %TBUILD%\%REPO_NAME%\Telegram

          call configure.bat ^
          %TDESKTOP_BUILD_GENERATOR% ^
          %TDESKTOP_BUILD_ARCH% ^
          %TDESKTOP_BUILD_API% ^
          -D CMAKE_CONFIGURATION_TYPES=Debug ^
          -D CMAKE_C_FLAGS="/WX" ^
          -D CMAKE_CXX_FLAGS="/WX" ^
          -D DESKTOP_APP_DISABLE_AUTOUPDATE=OFF ^
          -D DESKTOP_APP_DISABLE_CRASH_REPORTS=OFF ^
          -D DESKTOP_APP_NO_PDB=ON ^
          %TDESKTOP_BUILD_DEFINE%

          cmake --build ..\out --config Debug --parallel

      - name: Move artifact.
        if: (env.UPLOAD_ARTIFACT == 'true') || (github.ref == 'refs/heads/nightly')
        run: |
          set OUT=%TBUILD%\%REPO_NAME%\out\Debug
          mkdir artifact
          move %OUT%\Telegram.exe artifact/
          move %OUT%\Updater.exe artifact/
      - uses: actions/upload-artifact@v4
        name: Upload artifact.
        if: (env.UPLOAD_ARTIFACT == 'true') || (github.ref == 'refs/heads/nightly')
        with:
          name: ${{ env.ARTIFACT_NAME }}
          path: artifact\
