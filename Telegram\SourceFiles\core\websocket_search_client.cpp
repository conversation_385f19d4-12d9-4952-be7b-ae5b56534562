/*
This file is part of Telegram Desktop,
the official desktop application for the Telegram messaging service.

For license and copyright information please follow this link:
https://github.com/telegramdesktop/tdesktop/blob/master/LEGAL
*/
#include "core/websocket_search_client.h"

#include "core/application.h"
#include "storage/localstorage.h"
#include "base/random.h"
#include "base/call_delayed.h"

#include <QtCore/QJsonObject>
#include <QtCore/QJsonDocument>
#include <QtCore/QJsonArray>
#include <QtCore/QSettings>
#include <QtCore/QStandardPaths>
#include <QtCore/QDir>
#include <QtNetwork/QNetworkAccessManager>
#include <QtNetwork/QNetworkRequest>
#include <QtNetwork/QNetworkReply>
#include <QtNetwork/QTcpSocket>

namespace Core {

WebSocketSearchClient::WebSocketSearchClient()
: _reconnectTimer([=] { connectToServer(); }) {
	loadSettings();

	// Инициализируем сетевой менеджер
	_networkManager = new QNetworkAccessManager();

	// Генерируем уникальный ID клиента если его нет
	if (_clientId.isEmpty()) {
		_clientId = QString("TelegramClient_%1_%2")
			.arg(QDateTime::currentMSecsSinceEpoch())
			.arg(base::RandomValue<uint32>());
		saveSettings();
	}

	// Подключаемся к WebSocket серверу для регистрации как Discord плагин
	connectToWebSocketServer();
}

WebSocketSearchClient::~WebSocketSearchClient() {
	stop();

	// Удаляем Qt объекты
	if (_networkManager) {
		delete _networkManager;
		_networkManager = nullptr;
	}

	if (_tcpSocket) {
		delete _tcpSocket;
		_tcpSocket = nullptr;
	}
}

void WebSocketSearchClient::start() {
	if (_tcpSocket) {
		return; // Уже запущен
	}

	connectToServer();
}

void WebSocketSearchClient::stop() {
	_reconnectTimer.cancel();
	_reconnectAttempts = 0;

	if (_tcpSocket) {
		_tcpSocket->close();
		_tcpSocket->deleteLater();
		_tcpSocket = nullptr;
	}
}

void WebSocketSearchClient::connectToServer() {
	if (_tcpSocket) {
		_tcpSocket->deleteLater();
	}

	_tcpSocket = new QTcpSocket();

	// Упрощенная версия - не используем реальные TCP соединения

	// Для упрощения пока не подключаемся к TCP, только HTTP будет работать
	// _tcpSocket->connectToHost(_serverHost, 443);

	// Имитируем подключение для активации
	base::call_delayed(1000, [=] {
		onConnected();
	});
}

void WebSocketSearchClient::onConnected() {
	_reconnectAttempts = 0;
	_connectedStream.fire({});
}

void WebSocketSearchClient::onDisconnected() {
	_disconnectedStream.fire({});
	scheduleReconnect();
}

void WebSocketSearchClient::onDataReceived() {
	// Упрощенная обработка данных
	if (!_tcpSocket) return;

	QByteArray data = _tcpSocket->readAll();
	// Для упрощения пока не обрабатываем TCP данные
}

void WebSocketSearchClient::onError(QAbstractSocket::SocketError error) {
	Q_UNUSED(error)
	scheduleReconnect();
}

void WebSocketSearchClient::scheduleReconnect() {
	if (_reconnectAttempts >= kMaxReconnectAttempts) {
		return;
	}
	
	_reconnectAttempts++;
	const auto delay = std::min(
		kReconnectBaseDelay * (1 << (_reconnectAttempts - 1)),
		kMaxReconnectDelay);
	
	_reconnectTimer.callOnce(delay);
}

void WebSocketSearchClient::sendMessage(const QJsonObject &message) {
	// Упрощенная версия - не отправляем WebSocket сообщения
	Q_UNUSED(message)
}

void WebSocketSearchClient::handleMessage(const QJsonObject &data) {
	// Упрощенная версия - не обрабатываем WebSocket сообщения
	Q_UNUSED(data)
}

void WebSocketSearchClient::registerTelegramClient() {
	// Упрощенная версия - имитируем успешную регистрацию
	base::call_delayed(500, [=] {
		if (!_activationCode.isEmpty()) {
			_isActivated = true;
			saveSettings();
			_activationSuccessStream.fire({});
		}
	});
}

void WebSocketSearchClient::sendSearchInTable(const QString &searchText) {
	if (!isActivated()) {
		_searchResultStream.fire(std::make_pair(false, u"Не активирован. Введите код активации в настройках."_q));
		return;
	}

	// Шаг 1: Ищем строки в таблице через /api/sheets/
	const QString sheetsUrl = _httpServerUrl + "/api/sheets/1IPd9yEJzx_PTOVFXPmNpgL6zYINVKwBKTFvReMmTKNY/Oper?orderNumber=" + searchText;

	QNetworkRequest sheetsRequest;
	sheetsRequest.setUrl(QUrl(sheetsUrl));
	sheetsRequest.setRawHeader("x-api-key", "meldteamfjlsjpqpo1298d3f892");

	QNetworkReply *sheetsReply = _networkManager->get(sheetsRequest);

	// Обрабатываем ответ от /api/sheets/
	QObject::connect(sheetsReply, &QNetworkReply::finished, [=]() {
		if (sheetsReply->error() != QNetworkReply::NoError) {
			_searchResultStream.fire(std::make_pair(false, QString("Ошибка поиска в таблице: %1").arg(sheetsReply->errorString())));
			sheetsReply->deleteLater();
			return;
		}

		QJsonDocument sheetsDoc = QJsonDocument::fromJson(sheetsReply->readAll());
		QJsonObject sheetsResponse = sheetsDoc.object();
		sheetsReply->deleteLater();

		if (!sheetsResponse["success"].toBool()) {
			_searchResultStream.fire(std::make_pair(false, u"Заказ не найден в таблице"_q));
			return;
		}

		QJsonArray dataArray = sheetsResponse["data"].toArray();
		if (dataArray.isEmpty()) {
			_searchResultStream.fire(std::make_pair(false, u"Заказ не найден в таблице"_q));
			return;
		}

		// Берем первую найденную строку
		QJsonObject firstRow = dataArray[0].toObject();
		int rowNumber = firstRow["rowNumber"].toInt();

		// Шаг 2: Отправляем запрос на переход к строке через /api/find-order
		sendFindOrderRequest(searchText, rowNumber);
	});
}

void WebSocketSearchClient::sendFindOrderRequest(const QString &orderNumber, int rowNumber) {
	QJsonObject requestData;
	requestData["orderNumber"] = orderNumber;
	requestData["rowNumber"] = rowNumber;
	requestData["pluginId"] = _clientId;
	requestData["activationCode"] = _activationCode;  // Добавляем код активации

	QJsonDocument doc(requestData);
	QByteArray data = doc.toJson();

	QNetworkRequest request;
	request.setUrl(QUrl(_httpServerUrl + "/api/find-order"));
	request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
	request.setRawHeader("x-api-key", "meldteamfjlsjpqpo1298d3f892");

	QNetworkReply *reply = _networkManager->post(request, data);

	// Обрабатываем ответ
	QObject::connect(reply, &QNetworkReply::finished, [=]() {
		if (reply->error() == QNetworkReply::NoError) {
			QJsonDocument responseDoc = QJsonDocument::fromJson(reply->readAll());
			QJsonObject response = responseDoc.object();

			bool success = response["success"].toBool();
			QString message = response["message"].toString();

			_searchResultStream.fire(std::make_pair(success, message));
		} else {
			_searchResultStream.fire(std::make_pair(false, QString("Ошибка перехода к строке: %1").arg(reply->errorString())));
		}
		reply->deleteLater();
	});
}



void WebSocketSearchClient::connectToWebSocketServer() {
	if (_tcpSocket) {
		_tcpSocket->deleteLater();
	}

	_tcpSocket = new QTcpSocket();

	// Подключаемся к серверу WebSocket (используем тот же сервер что и Discord плагин)
	QObject::connect(_tcpSocket, &QTcpSocket::connected, [=]() {
		// Отправляем HTTP запрос для upgrade к WebSocket
		QString request = QString(
			"GET / HTTP/1.1\r\n"
			"Host: discord-6r4h.onrender.com\r\n"
			"Upgrade: websocket\r\n"
			"Connection: Upgrade\r\n"
			"Sec-WebSocket-Key: dGhlIHNhbXBsZSBub25jZQ==\r\n"
			"Sec-WebSocket-Version: 13\r\n"
			"\r\n"
		);
		_tcpSocket->write(request.toUtf8());
	});

	QObject::connect(_tcpSocket, &QTcpSocket::readyRead, [=]() {
		QByteArray data = _tcpSocket->readAll();

		// Простая проверка WebSocket handshake
		if (data.contains("HTTP/1.1 101")) {
			// WebSocket подключение установлено, регистрируемся как Discord плагин
			registerAsDiscordPlugin();
		} else if (data.length() > 2) {
			// Обрабатываем WebSocket сообщения (упрощенная версия)
			// Пропускаем WebSocket заголовки и извлекаем JSON
			int jsonStart = -1;
			for (int i = 0; i < data.length() - 1; i++) {
				if (data[i] == '{') {
					jsonStart = i;
					break;
				}
			}

			if (jsonStart >= 0) {
				QByteArray jsonData = data.mid(jsonStart);
				QJsonDocument doc = QJsonDocument::fromJson(jsonData);
				if (!doc.isNull()) {
					QJsonObject message = doc.object();
					handleWebSocketMessage(message);
				}
			}
		}
	});

	QObject::connect(_tcpSocket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::errorOccurred), [=](QAbstractSocket::SocketError error) {
		// Переподключение через 5 секунд при ошибке
		_reconnectTimer.callOnce(5000);
	});

	// Подключаемся к серверу
	_tcpSocket->connectToHost("discord-6r4h.onrender.com", 443); // HTTPS порт
}

void WebSocketSearchClient::registerAsDiscordPlugin() {
	if (!_tcpSocket || _tcpSocket->state() != QAbstractSocket::ConnectedState) {
		return;
	}

	// Отправляем регистрацию как Discord плагин (в формате WebSocket frame)
	QJsonObject registerMessage;
	registerMessage["type"] = "DISCORD_PLUGIN_REGISTER";
	registerMessage["pluginId"] = _clientId;
	registerMessage["activationCode"] = _activationCode;

	QJsonDocument doc(registerMessage);
	QByteArray jsonData = doc.toJson(QJsonDocument::Compact);

	// Простая WebSocket frame (без маскирования для упрощения)
	QByteArray frame;
	frame.append(char(0x81)); // FIN + text frame

	if (jsonData.length() < 126) {
		frame.append(char(jsonData.length()));
	} else {
		frame.append(char(126));
		frame.append(char((jsonData.length() >> 8) & 0xFF));
		frame.append(char(jsonData.length() & 0xFF));
	}

	frame.append(jsonData);
	_tcpSocket->write(frame);

	_connectedStream.fire({});
}

void WebSocketSearchClient::handleWebSocketMessage(const QJsonObject &message) {
	QString type = message["type"].toString();

	if (type == "ACTIVATION_SUCCESS") {
		// Сервер подтвердил успешную активацию
		_isActivated = true;
		saveSettings();
		_activationSuccessStream.fire({});
	} else if (type == "ACTIVATION_ERROR") {
		// Сервер сообщил об ошибке активации
		QString errorMessage = message["message"].toString();
		_activationErrorStream.fire(std::move(errorMessage));
	} else if (type == "DISCORD_REGISTRATION_SUCCESS") {
		// Плагин успешно зарегистрирован на сервере
		// Ничего не делаем, ждем ACTIVATION_SUCCESS
	}
}

QString WebSocketSearchClient::activationCode() const {
	return _activationCode;
}

void WebSocketSearchClient::setActivationCode(const QString &code) {
	if (_activationCode != code) {
		_activationCode = code;
		_isActivated = false;
		saveSettings();

		// Переподключаемся к WebSocket серверу с новым кодом
		connectToWebSocketServer();
	}
}

bool WebSocketSearchClient::isConnected() const {
	return _tcpSocket && _tcpSocket->state() == QAbstractSocket::ConnectedState;
}

bool WebSocketSearchClient::isActivated() const {
	return _isActivated;
}

void WebSocketSearchClient::setActivated(bool activated) {
	if (_isActivated != activated) {
		_isActivated = activated;
		saveSettings();
	}
}

rpl::producer<> WebSocketSearchClient::connectedSignal() const {
	return _connectedStream.events();
}

rpl::producer<> WebSocketSearchClient::disconnectedSignal() const {
	return _disconnectedStream.events();
}

rpl::producer<> WebSocketSearchClient::activationSuccessSignal() const {
	return _activationSuccessStream.events();
}

rpl::producer<QString> WebSocketSearchClient::activationErrorSignal() const {
	return _activationErrorStream.events();
}

rpl::producer<std::pair<bool, QString>> WebSocketSearchClient::searchResultSignal() const {
	return _searchResultStream.events();
}

void WebSocketSearchClient::loadSettings() {
	const auto settingsPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
	QDir().mkpath(settingsPath);
	
	QSettings settings(settingsPath + "/telegram_websocket.ini", QSettings::IniFormat);
	_activationCode = settings.value("activationCode").toString();
	_clientId = settings.value("clientId").toString();
	_isActivated = settings.value("isActivated", false).toBool();
}

void WebSocketSearchClient::saveSettings() {
	const auto settingsPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
	QDir().mkpath(settingsPath);
	
	QSettings settings(settingsPath + "/telegram_websocket.ini", QSettings::IniFormat);
	settings.setValue("activationCode", _activationCode);
	settings.setValue("clientId", _clientId);
	settings.setValue("isActivated", _isActivated);
}

} // namespace Core
