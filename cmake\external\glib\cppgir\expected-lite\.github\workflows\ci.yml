name: CI

env:
  PROJECT: EXPECTED_LITE

on:
  push:
    branches: [ master ]

  pull_request:
    branches: [ master ]

  workflow_dispatch:

jobs:
  gcc:
    strategy:
      matrix:
        version: [8, 9, 10, 11]

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Install GCC ${{ matrix.version }}
        run: sudo apt-get install -y gcc-${{ matrix.version }} g++-${{ matrix.version }}

      - name: Configure tests
        env:
          CXX: g++-${{ matrix.version }}
        run: cmake -S . -B build
          -D CMAKE_BUILD_TYPE:STRING=Release
          -D ${{ env.PROJECT }}_OPT_SELECT_NONSTD=ON
          -D ${{ env.PROJECT }}_OPT_BUILD_TESTS=ON
          -D ${{ env.PROJECT }}_OPT_BUILD_EXAMPLES=OFF

      - name: Build tests
        run: cmake --build build -j 4

      - name: Run tests
        working-directory: build
        run: ctest --output-on-failure -j 4

  clang:
    strategy:
      matrix:
        version: [8, 9, 10, 11, 12]

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Install Clang ${{ matrix.version }}
        run: sudo apt-get install -y clang-${{ matrix.version }}

      - name: Configure tests
        env:
          CXX: clang-${{ matrix.version }}
        run: cmake -S . -B build
          -D CMAKE_CXX_COMPILER=clang++
          -D CMAKE_BUILD_TYPE:STRING=Release
          -D ${{ env.PROJECT }}_OPT_SELECT_NONSTD=ON
          -D ${{ env.PROJECT }}_OPT_BUILD_TESTS=ON
          -D ${{ env.PROJECT }}_OPT_BUILD_EXAMPLES=OFF

      - name: Build tests
        run: cmake --build build -j 4

      - name: Run tests
        working-directory: build
        run: ctest --output-on-failure -j 4

  msvc:
    strategy:
      matrix:
        os: [windows-2019, windows-2022]

    runs-on: ${{ matrix.os }}

    steps:
      - uses: actions/checkout@v3

      - name: Configure tests
        run: cmake -S . -B build
          -D ${{ env.PROJECT }}_OPT_SELECT_NONSTD=ON
          -D ${{ env.PROJECT }}_OPT_BUILD_TESTS=ON
          -D ${{ env.PROJECT }}_OPT_BUILD_EXAMPLES=OFF

      - name: Build tests
        run: cmake --build build --config Release -j 4

      - name: Run tests
        working-directory: build
        run: ctest -C Release --output-on-failure -j 4

