<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkListStore" id="liststore1">
    <columns>
      <column type="gchararray"/>
      <column type="gchararray"/>
      <column type="gint"/>
      <column type="gchararray"/>
    </columns>
    <data>
      <row>
        <col id="0" translatable="yes">John</col>
        <col id="1" translatable="yes">Doe</col>
        <col id="2">25</col>
        <col id="3" translatable="yes">This is the John Doe row</col>
      </row>
      <row>
        <col id="0" translatable="yes">Mary</col>
        <col id="1" translatable="yes">Unknown</col>
        <col id="2">50</col>
        <col id="3" translatable="yes">This is the Mary Unknown row</col>
      </row>
    </data>
  </object>
  <object class="GtkWindow" id="window1">
    <property name="default-height">250</property>
    <property name="default-width">440</property>
    <property name="title" translatable="yes">Gtk::Builder demo</property>
    <child>
      <object class="GtkBox" id="vbox1">
        <property name="orientation">vertical</property>
        <child>
          <object class="GtkBox" id="toolbar1">
            <child>
              <object class="GtkButton">
                <property name="label" translatable="yes">Help</property>
                <property name="tooltip-text" translatable="yes">Help</property>
                <property name="action-name">win.help</property>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkScrolledWindow" id="scrolledwindow1">
            <property name="hexpand">True</property>
            <property name="vexpand">True</property>
            <child>
              <object class="GtkTreeView" id="treeview1">
                <property name="model">liststore1</property>
                <property name="tooltip-column">3</property>
                <child>
                  <object class="GtkTreeViewColumn" id="column1">
                    <property name="title">Name</property>
                    <child>
                      <object class="GtkCellRendererText" id="renderer1"/>
                      <attributes>
                        <attribute name="text">0</attribute>
                      </attributes>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkTreeViewColumn" id="column2">
                    <property name="title">Surname</property>
                    <child>
                      <object class="GtkCellRendererText" id="renderer2"/>
                      <attributes>
                        <attribute name="text">1</attribute>
                      </attributes>
                    </child>
                  </object>
                </child>
                <child>
                  <object class="GtkTreeViewColumn" id="column3">
                    <property name="title">Age</property>
                    <child>
                      <object class="GtkCellRendererText" id="renderer3"/>
                      <attributes>
                        <attribute name="text">2</attribute>
                      </attributes>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkStatusbar" id="statusbar1"/>
        </child>
      </object>
    </child>
  </object>
</interface>
