variables:
  GIT_SUBMODULE_STRATEGY: recursive

stages:
  - build

default:
  image: registry.gitlab.com/mnauw/cppgir:jammy

ubuntu-focal-gcc:
  image: registry.gitlab.com/mnauw/cppgir:focal
  stage: build
  script:
    - mkdir build-gcc
    - cd build-gcc
    - cmake ..
    - make
    - make examples
  except:
    - tags

ubuntu-focal-clang:
  image: registry.gitlab.com/mnauw/cppgir:focal
  stage: build
  script:
    - mkdir build-clang
    - cd build-clang
    - CC=clang-11 CXX=clang++-11 cmake ..
    - make
    - make examples
  except:
    - tags

ubuntu-jammy-gcc-11:
  stage: build
  script:
    - mkdir build-gcc-11
    - cd build-gcc-11
    - cmake ..
    - make
    - make examples
  except:
    - tags

ubuntu-jammy-clang-13:
  stage: build
  script:
    - mkdir build-clang-13
    - cd build-clang-13
    - CC=clang-13 CXX=clang++-13 cmake ..
    - make
    - make examples
  except:
    - tags

ubuntu-jammy-clang-14:
  stage: build
  script:
    - mkdir build-clang-14
    - cd build-clang-14
    - CC=clang-14 CXX=clang++-14 cmake ..
    - make
    - make examples
  except:
    - tags
