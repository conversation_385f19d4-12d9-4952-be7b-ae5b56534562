#include "genutils.hpp"

#include <set>
#include <string>

static std::set<std::string> reserved{"alignas", "alignof", "and", "and_eq",
    "asm", "atomic_cancel", "atomic_commit", "atomic_noexcept", "auto",
    "bitand", "bitor", "bool", "break", "case", "catch", "char", "char16_t",
    "char32_t", "class", "compl", "concept", "const", "constexpr", "const_cast",
    "continue", "co_await", "co_return", "co_yield", "decltype", "default",
    "delete", "do", "double", "dynamic_cast", "else", "enum", "explicit",
    "export", "extern", "false", "float", "for", "friend", "goto", "if",
    "import", "inline", "int", "long", "module", "mutable", "namespace", "new",
    "noexcept", "not", "not_eq", "nullptr", "operator", "or", "or_eq",
    "private", "protected", "public", "register", "reflexpr",
    "reinterpret_cast", "requires", "return", "short", "signed", "sizeof",
    "static", "static_assert", "static_cast", "struct", "switch",
    "synchronized", "template", "this", "thread_local", "throw", "true", "try",
    "typedef", "typeid", "typename", "union", "unsigned", "using", "virtual",
    "void", "volatile", "wchar_t", "while", "xor", "xor_eq"};

std::string
unreserve(const std::string &s, bool force)
{
  auto res(s);
  if (!s.empty() && isdigit(s[0]))
    res.insert(res.begin(), '_');
  else if (reserved.find(s) != reserved.end() || toupper(s) == s || force)
    res += "_";
  return res;
}
